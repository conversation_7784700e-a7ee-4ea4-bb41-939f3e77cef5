"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { Play } from "lucide-react";
import { Movie } from "@/lib/data";

// Helper function to convert YouTube URL to embed URL
const getYouTubeEmbedUrl = (url: string): string => {
  const youtubeRegex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/;
  const match = url.match(youtubeRegex);
  if (match) {
    return `https://www.youtube.com/embed/${match[1]}?autoplay=1&mute=1&loop=1&playlist=${match[1]}`;
  }
  return url;
};

// Helper function to check if URL is a YouTube URL
const isYouTubeUrl = (url: string): boolean => {
  return url.includes("youtube.com") || url.includes("youtu.be");
};

type HeroSectionProps = React.FC<{
  movies: Movie[];
}>;
const HeroSection: HeroSectionProps = ({ movies }) => {
  return (
    <section className="w-full">
      <Carousel
        opts={{ loop: true }}
        plugins={[
          Autoplay({
            delay: 8000, // Increased delay for video content
          }),
        ]}
        className="w-full"
      >
        <CarouselContent>
          {movies.map((movie) => (
            <CarouselItem key={movie.id}>
              <div className="relative h-[70vh] min-h-[450px] w-full flex items-center justify-start text-left text-white">
                {/* Video Background */}
                {isYouTubeUrl(movie.src) ? (
                  <iframe
                    src={getYouTubeEmbedUrl(movie.src)}
                    className="absolute inset-0 w-full h-full object-cover"
                    allow="autoplay; encrypted-media"
                    allowFullScreen
                    style={{ border: "none" }}
                  />
                ) : (
                  // Fallback for non-YouTube URLs - could be enhanced to support other video platforms
                  <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-purple-900 to-blue-900">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <p className="text-white/60 text-lg">
                        Video Preview Available
                      </p>
                    </div>
                  </div>
                )}

                {/* Dark overlay for better text readability */}
                <div className="absolute inset-0 bg-black/50" />

                {/* Content overlay */}
                <div className="relative z-10 container mx-auto px-4">
                  <div className="max-w-2xl">
                    <h1 className="text-4xl md:text-6xl font-headline font-bold drop-shadow-lg">
                      {movie.title}
                    </h1>
                    <p className="mt-4 text-lg md:text-xl text-white/90 drop-shadow-md">
                      {movie.genre} • {movie.tags?.join(", ")}
                    </p>
                    <p className="mt-2 text-sm text-white/70">
                      Published: {movie.publishedOn}
                    </p>
                    <div className="mt-8">
                      <Button asChild size="lg" className="font-bold text-lg">
                        <Link
                          href={movie.src}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Play className="fill-current" />
                          Watch Now
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 z-20 text-white bg-black/30 hover:bg-black/50 border-none" />
        <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 z-20 text-white bg-black/30 hover:bg-black/50 border-none" />
      </Carousel>
    </section>
  );
};

export default HeroSection;
