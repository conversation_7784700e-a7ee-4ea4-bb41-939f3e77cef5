"use client";

import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { Play } from "lucide-react";
import { Movie } from "@/lib/data";

type HeroSectionProps = React.FC<{
  movies: Movie[];
}>;
const HeroSection: HeroSectionProps = ({ movies }) => {
  const slides = [
    {
      id: 1,
      image: "https://placehold.co/1920x1080.png",
      hint: "futuristic city",
      title: "The Future of Cinema is Here",
      description:
        "Dive into a new era of storytelling with groundbreaking films generated by artificial intelligence.",
      buttonLink: "/films",
    },
    {
      id: 2,
      image: "https://placehold.co/1920x1080.png",
      hint: "glowing forest",
      title: "Experience Unseen Worlds",
      description:
        "Journey through fantastical landscapes and mind-bending narratives crafted by AI.",
      buttonLink: "/films",
    },
    {
      id: 3,
      image: "https://placehold.co/1920x1080.png",
      hint: "robot human",
      title: "Where Creativity Meets Code",
      description:
        "Witness the fusion of human imagination and artificial intelligence in every frame.",
      buttonLink: "/films",
    },
  ];

  return (
    <section className="w-full">
      <Carousel
        opts={{ loop: true }}
        plugins={[
          Autoplay({
            delay: 5000,
          }),
        ]}
        className="w-full"
      >
        <CarouselContent>
          {movies.map((slide) => (
            <CarouselItem key={slide.id}>
              <div className="relative h-[70vh] min-h-[450px] w-full flex items-center justify-start text-left text-white">
                <Image
                  src={slide.image}
                  alt={slide.title}
                  data-ai-hint={slide.hint}
                  fill
                  className="object-cover"
                  priority={slide.id === 1}
                />
                <div className="absolute inset-0 bg-black/60" />
                <div className="relative z-10 container mx-auto px-4">
                  <div className="max-w-2xl">
                    <h1 className="text-4xl md:text-6xl font-headline font-bold drop-shadow-lg">
                      {slide.title}
                    </h1>
                    <p className="mt-4 text-lg md:text-xl text-white/80 drop-shadow-md">
                      {slide.description}
                    </p>
                    <div className="mt-8">
                      <Button asChild size="lg" className="font-bold text-lg">
                        <Link href={slide.buttonLink}>
                          <Play className="fill-current" />
                          Watch Now
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 z-20 text-white bg-black/30 hover:bg-black/50 border-none" />
        <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 z-20 text-white bg-black/30 hover:bg-black/50 border-none" />
      </Carousel>
    </section>
  );
};

export default HeroSection;
