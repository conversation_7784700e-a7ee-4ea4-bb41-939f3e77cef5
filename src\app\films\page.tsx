import { getAllMovies, getGenres } from "@/lib/db";
import { movieToFilm } from "@/lib/data";
import { FilmsClient } from "./films-client";

export default async function FilmsPage() {
  // Fetch all movies and genres from the database
  const movies = await getAllMovies();
  const films = movies.map(movieToFilm);
  const genres = await getGenres();

  return <FilmsClient films={films} genres={genres} />;
}
