import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { HeroSection } from "@/components/HeroSection";
import { FilmCard } from "@/components/FilmCard";
import { movieToFilm } from "@/lib/data";
import { getMovies, getGenres } from "@/lib/db";
import { ArrowRight } from "lucide-react";
import { Faq } from "@/components/Faq";

export default async function Home() {
  // Fetch 5 movies from the database
  const movies = await getMovies(5);
  const featuredFilms = movies.slice(0, 3).map(movieToFilm);

  // Fetch genres from the database
  const genres = await getGenres();

  console.log({
    movies,
    featuredFilms,
    genres,
  });

  return (
    <div className="flex flex-col">
      <HeroSection movies={movies} />
      <section className="py-16 md:py-24 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-headline font-bold">
              Featured Films
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-lg text-muted-foreground">
              A curated selection of our most captivating AI-generated shorts.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredFilms.map((film) => (
              <FilmCard key={film.id} film={film} />
            ))}
          </div>

          <div className="mt-12 text-center">
            <Button asChild size="lg" variant="outline">
              <Link href="/films">
                View All Films <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      <section className="py-16 md:py-24 bg-background border-t border-border/40">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-headline font-bold">
              Browse By Genre
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-lg text-muted-foreground">
              Explore different worlds, one genre at a time.
            </p>
          </div>
          <div className="flex flex-wrap items-center justify-center gap-4 max-w-4xl mx-auto">
            {genres.map((genre) => (
              <Button key={genre} asChild variant="secondary" size="lg">
                <Link href={`/films?genre=${encodeURIComponent(genre)}`}>
                  {genre}
                </Link>
              </Button>
            ))}
          </div>
          <div className="mt-12 text-center">
            <Button asChild size="lg" variant="outline">
              <Link href="/films">
                See All <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      <Faq />
    </div>
  );
}
