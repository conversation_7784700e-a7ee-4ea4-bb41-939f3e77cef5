import { neon } from '@neondatabase/serverless';
import { Movie } from './data';

// Initialize NeonDB connection
const sql = neon(process.env.DATABASE_URL!);

/**
 * Fetch movies from the database
 * @param limit - Number of movies to fetch (default: 5)
 * @returns Promise<Movie[]>
 */
export async function getMovies(limit: number = 5): Promise<Movie[]> {
  try {
    const movies = await sql`
      SELECT id, title, tags, genre, src, "publishedOn"
      FROM movies
      ORDER BY id DESC
      LIMIT ${limit}
    `;
    
    return movies as Movie[];
  } catch (error) {
    console.error('Error fetching movies:', error);
    return [];
  }
}

/**
 * Fetch all movies from the database
 * @returns Promise<Movie[]>
 */
export async function getAllMovies(): Promise<Movie[]> {
  try {
    const movies = await sql`
      SELECT id, title, tags, genre, src, "publishedOn"
      FROM movies
      ORDER BY id DESC
    `;
    
    return movies as Movie[];
  } catch (error) {
    console.error('Error fetching all movies:', error);
    return [];
  }
}

/**
 * Fetch movies by genre
 * @param genre - Genre to filter by
 * @param limit - Number of movies to fetch
 * @returns Promise<Movie[]>
 */
export async function getMoviesByGenre(genre: string, limit?: number): Promise<Movie[]> {
  try {
    const query = limit 
      ? sql`
          SELECT id, title, tags, genre, src, "publishedOn"
          FROM movies
          WHERE genre = ${genre}
          ORDER BY id DESC
          LIMIT ${limit}
        `
      : sql`
          SELECT id, title, tags, genre, src, "publishedOn"
          FROM movies
          WHERE genre = ${genre}
          ORDER BY id DESC
        `;
    
    const movies = await query;
    return movies as Movie[];
  } catch (error) {
    console.error('Error fetching movies by genre:', error);
    return [];
  }
}

/**
 * Get all unique genres from the database
 * @returns Promise<string[]>
 */
export async function getGenres(): Promise<string[]> {
  try {
    const result = await sql`
      SELECT DISTINCT genre
      FROM movies
      WHERE genre IS NOT NULL
      ORDER BY genre
    `;
    
    return result.map((row: any) => row.genre);
  } catch (error) {
    console.error('Error fetching genres:', error);
    return [];
  }
}
